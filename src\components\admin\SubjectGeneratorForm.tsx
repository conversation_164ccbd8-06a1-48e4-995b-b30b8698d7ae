import React, { useState } from 'react';
import { useSubjectGeneration } from '../../hooks/useSubjectGeneration';
import { Subject } from '../../hooks/useSubjects';
import { LLMProvider } from '../../services/llmAdapter';
import LLMProviderSelector from './LLMProviderSelector';

/**
 * Form for generating and saving subjects using LLM
 */
const SubjectGeneratorForm: React.FC = () => {
  const [prompt, setPrompt] = useState<string>('');
  const [generatedSubject, setGeneratedSubject] = useState<any | null>(null);
  const [savedSubject, setSavedSubject] = useState<Subject | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  // LLM Provider state
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.OPENAI);
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');
  
  const { generateSubject, saveSubject, loading, error } = useSubjectGeneration();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) return;

    setGenerating(true);
    try {
      const result = await generateSubject(prompt, selectedProvider, selectedModel);
      setGeneratedSubject(result);
    } catch (err) {
      console.error('Error generating subject:', err);
    } finally {
      setGenerating(false);
    }
  };

  // Handle saving the generated subject
  const handleSave = async () => {
    if (!generatedSubject) return;
    
    setSaving(true);
    try {
      const result = await saveSubject(generatedSubject);
      if (result) {
        setSavedSubject(result);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 5000);
      }
    } catch (err) {
      console.error('Error saving subject:', err);
    } finally {
      setSaving(false);
    }
  };

  // Handle editing the generated subject
  const handleEdit = (field: string, value: string) => {
    if (!generatedSubject) return;
    
    setGeneratedSubject({
      ...generatedSubject,
      [field]: value
    });
  };

  // Reset the form
  const handleReset = () => {
    setPrompt('');
    setGeneratedSubject(null);
    setSavedSubject(null);
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Generate New Subject</h2>
      
      {/* Input form */}
      <form onSubmit={handleSubmit} className="mb-6">
        {/* LLM Provider Selection */}
        <div className="mb-6">
          <LLMProviderSelector
            selectedProvider={selectedProvider}
            selectedModel={selectedModel}
            onProviderChange={setSelectedProvider}
            onModelChange={setSelectedModel}
            disabled={generating || loading}
          />
        </div>

        <div className="mb-4">
          <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-1">
            Describe the subject you want to generate:
          </label>
          <textarea
            id="prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            rows={3}
            placeholder="E.g., Generate a detailed IGCSE Biology subject covering key topics for grades 9-10"
            disabled={generating || loading}
          />
        </div>
        
        <div className="flex gap-2">
          <button
            type="submit"
            disabled={generating || loading || !prompt.trim()}
            className={`px-4 py-2 rounded-md text-white ${
              generating || loading || !prompt.trim()
                ? 'bg-blue-300'
                : 'bg-blue-600 hover:bg-blue-700'
            } transition-colors`}
          >
            {generating || loading ? 'Generating...' : 'Generate Subject'}
          </button>
          
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>
        </div>
      </form>
      
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      {/* Generated subject preview */}
      {generatedSubject && (
        <div className="border rounded-md p-4 mb-6">
          <h3 className="text-lg font-medium mb-4">Generated Subject</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name:</label>
              <input
                type="text"
                value={generatedSubject.name}
                onChange={(e) => handleEdit('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Code:</label>
              <input
                type="text"
                value={generatedSubject.code}
                onChange={(e) => handleEdit('code', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description:</label>
              <textarea
                value={generatedSubject.description}
                onChange={(e) => handleEdit('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows={4}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Color:</label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={generatedSubject.color_hex}
                  onChange={(e) => handleEdit('color_hex', e.target.value)}
                  className="w-10 h-10 border border-gray-300 rounded"
                />
                <input
                  type="text"
                  value={generatedSubject.color_hex}
                  onChange={(e) => handleEdit('color_hex', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Icon Name:</label>
              <input
                type="text"
                value={generatedSubject.icon_name}
                onChange={(e) => handleEdit('icon_name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <button
              onClick={handleSave}
              disabled={saving || loading}
              className={`px-4 py-2 rounded-md text-white ${
                saving || loading
                  ? 'bg-green-300'
                  : 'bg-green-600 hover:bg-green-700'
              } transition-colors`}
            >
              {saving || loading ? 'Saving...' : 'Save Subject'}
            </button>
          </div>
        </div>
      )}
      
      {/* Success message */}
      {showSuccess && savedSubject && (
        <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          Subject "{savedSubject.name}" has been successfully saved!
        </div>
      )}
      
      {/* Example prompt suggestions */}
      <div className="mt-8">
        <h3 className="text-lg font-medium mb-2">Example Prompts</h3>
        <div className="space-y-2">
          <button
            onClick={() => setPrompt('Generate a detailed IGCSE Biology subject covering key topics for grades 9-10')}
            className="block w-full text-left px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Generate a detailed IGCSE Biology subject
          </button>
          <button
            onClick={() => setPrompt('Create a Physics subject for IGCSE students with appropriate topics and description')}
            className="block w-full text-left px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Create a Physics subject for IGCSE students
          </button>
          <button
            onClick={() => setPrompt('Generate a Mathematics subject for IGCSE curriculum with all essential topics')}
            className="block w-full text-left px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Generate a Mathematics subject for IGCSE curriculum
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubjectGeneratorForm;
