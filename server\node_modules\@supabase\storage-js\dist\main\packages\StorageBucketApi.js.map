{"version": 3, "file": "StorageBucketApi.js", "sourceRoot": "", "sources": ["../../../src/packages/StorageBucketApi.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,gDAAkD;AAClD,0CAA4D;AAC5D,wCAA4D;AAC5D,4CAA6C;AAG7C,MAAqB,gBAAgB;IAKnC,YAAY,GAAW,EAAE,UAAqC,EAAE,EAAE,KAAa;QAC7E,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,mCAAQ,2BAAe,GAAK,OAAO,CAAE,CAAA;QACjD,IAAI,CAAC,KAAK,GAAG,IAAA,sBAAY,EAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACG,WAAW;;YAUf,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAA,WAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;gBACnF,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,uBAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;OAIG;IACG,SAAS,CACb,EAAU;;YAWV,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAA,WAAG,EAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;gBACzF,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,uBAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;OAYG;IACG,YAAY,CAChB,EAAU,EACV,UAII;QACF,MAAM,EAAE,KAAK;KACd;;YAWD,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,SAAS,EACpB;oBACE,EAAE;oBACF,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,eAAe,EAAE,OAAO,CAAC,aAAa;oBACtC,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;iBAC7C,EACD,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,uBAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;;;;;OAWG;IACG,YAAY,CAChB,EAAU,EACV,OAIC;;YAWD,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAA,WAAG,EACpB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,WAAW,EAAE,EAAE,EAC1B;oBACE,EAAE;oBACF,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,eAAe,EAAE,OAAO,CAAC,aAAa;oBACtC,kBAAkB,EAAE,OAAO,CAAC,gBAAgB;iBAC7C,EACD,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,uBAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;OAIG;IACG,WAAW,CACf,EAAU;;YAWV,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAA,YAAI,EACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,WAAW,EAAE,QAAQ,EAChC,EAAE,EACF,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,uBAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,YAAY,CAChB,EAAU;;YAWV,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAA,cAAM,EACvB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,WAAW,EAAE,EAAE,EAC1B,EAAE,EACF,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,uBAAc,EAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;CACF;AAhPD,mCAgPC"}