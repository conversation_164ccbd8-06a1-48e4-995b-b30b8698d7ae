import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

export interface Topic {
  id: string;
  subject_id: string;
  parent_topic_id: string | null;
  title: string;
  slug: string;
  description: string | null;
  content: string | null;
  difficulty_level: number;
  estimated_study_time_minutes: number;
  learning_objectives: string[] | null;
  display_order: number;
  is_published: boolean;
}

export function useTopics(subjectId: string | null) {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  useEffect(() => {
    // Don't fetch if no subjectId is provided
    if (!subjectId) {
      setTopics([]);
      return;
    }

    const fetchTopics = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const { data, error } = await supabase
          .from('topics')
          .select('*')
          .eq('subject_id', subjectId)
          .order('display_order', { ascending: true });
        
        if (error) {
          throw new Error(error.message);
        }
        
        setTopics(data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching topics:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();

    const handleTopicsChanged = () => {
      if (subjectId) {
        fetchTopics();
      }
    };

    document.addEventListener('topicsChanged', handleTopicsChanged);

    return () => {
      document.removeEventListener('topicsChanged', handleTopicsChanged);
    };
  }, [subjectId]);

  const saveTopics = async (subjectId: string, topicTitles: string[]): Promise<boolean> => {
    try {
      setIsSaving(true);
      setSaveError(null);

      const topicsToInsert = topicTitles.map(title => ({
        title,
        subject_id: subjectId,
        // Generate a simple slug from the title
        slug: title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, ''),
      }));

      const { error } = await supabase.from('topics').insert(topicsToInsert);

      if (error) {
        throw new Error(error.message);
      }

      // Optionally, refetch topics for the current subject to update the list
      if (subjectId) {
        const event = new Event('topicsChanged');
        document.dispatchEvent(event);
      }
      return true;

    } catch (err) {
      setSaveError(err instanceof Error ? err.message : 'An unknown error occurred while saving.');
      console.error('Error saving topics:', err);
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const saveSingleTopic = async (topic: Partial<Topic>): Promise<boolean> => {
    if (!topic.title || !topic.subject_id) {
      setSaveError('Topic title and subject ID are required.');
      return false;
    }

    try {
      setIsSaving(true);
      setSaveError(null);

      const topicToSave = {
        ...topic,
        slug: topic.slug || topic.title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, ''),
      };

      const { error } = await supabase
        .from('topics')
        .upsert(topicToSave, { onConflict: 'subject_id, title' });

      if (error) {
        throw new Error(error.message);
      }

      // Dispatch event to notify other components of the change
      document.dispatchEvent(new Event('topicsChanged'));
      return true;

    } catch (err) {
      setSaveError(err instanceof Error ? err.message : 'An unknown error occurred while saving the topic.');
      console.error('Error saving single topic:', err);
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  return { topics, loading, error, saveTopics, saveSingleTopic, isSaving, saveError };
}
