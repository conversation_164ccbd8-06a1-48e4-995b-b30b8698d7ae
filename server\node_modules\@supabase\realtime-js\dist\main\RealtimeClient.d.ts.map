{"version": 3, "file": "RealtimeClient.d.ts", "sourceRoot": "", "sources": ["../../src/RealtimeClient.ts"], "names": [], "mappings": "AAEA,OAAO,EAEL,gBAAgB,EAOjB,MAAM,iBAAiB,CAAA;AAExB,OAAO,UAAU,MAAM,kBAAkB,CAAA;AACzC,OAAO,KAAK,MAAM,aAAa,CAAA;AAG/B,OAAO,eAAe,MAAM,mBAAmB,CAAA;AAC/C,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAA;AAE/D,KAAK,KAAK,GAAG,OAAO,KAAK,CAAA;AAEzB,MAAM,MAAM,OAAO,GAAG;IACpB,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,UAAU,EAAE,MAAM,CAAA;IAClB,EAAE,EAAE,MAAM,CAAA;CACX,CAAA;AACD,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;AAEhD,MAAM,MAAM,eAAe,GAAG;IAC5B,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,GAAG,CAAA;IACZ,GAAG,EAAE,MAAM,CAAA;IACX,QAAQ,CAAC,EAAE,MAAM,CAAA;CAClB,CAAA;AAED,MAAM,MAAM,6BAA6B,GAAG,IAAI,GAAG,WAAW,GAAG,OAAO,CAAA;AACxE,MAAM,MAAM,eAAe,GACvB,MAAM,GACN,IAAI,GACJ,OAAO,GACP,SAAS,GACT,cAAc,CAAA;AAIlB,MAAM,WAAW,wBAAwB;IACvC,KACE,OAAO,EAAE,MAAM,GAAG,GAAG,EACrB,QAAQ,CAAC,EAAE,GAAG,EACd,OAAO,CAAC,EAAE;QAAE,OAAO,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GACxC,aAAa,CAAA;CACjB;AAED,MAAM,MAAM,aAAa,GAAG,SAAS,GAAG,gBAAgB,CAAA;AAExD,MAAM,WAAW,kBAAkB;IACjC,KAAK,EAAE,GAAG,CAAA;IACV,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;CACb;AAED,MAAM,MAAM,qBAAqB,GAAG;IAClC,SAAS,CAAC,EAAE,wBAAwB,CAAA;IACpC,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,mBAAmB,CAAC,EAAE,MAAM,CAAA;IAC5B,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,gBAAgB,CAAC,EAAE,QAAQ,CAAA;IAC3B,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAA;IACnC,MAAM,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAA;IAE/B,SAAS,CAAC,EAAE,QAAQ,CAAA;IACpB,QAAQ,CAAC,EAAE,QAAQ,CAAA;IACnB,KAAK,CAAC,EAAE,KAAK,CAAA;IACb,MAAM,CAAC,EAAE,OAAO,CAAA;IAChB,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,WAAW,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAA;CAC3C,CAAA;AASD,MAAM,CAAC,OAAO,OAAO,cAAc;IACjC,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAO;IACtC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAO;IAC5B,QAAQ,EAAE,eAAe,EAAE,CAAc;IACzC,QAAQ,EAAE,MAAM,CAAK;IACrB,YAAY,EAAE,MAAM,CAAK;IACzB,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAkB;IACrD,MAAM,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAK;IACvC,OAAO,EAAE,MAAM,CAAkB;IACjC,SAAS,EAAE,wBAAwB,GAAG,IAAI,CAAA;IAC1C,mBAAmB,EAAE,MAAM,CAAQ;IACnC,cAAc,EAAE,UAAU,CAAC,OAAO,WAAW,CAAC,GAAG,SAAS,CAAY;IACtE,mBAAmB,EAAE,MAAM,GAAG,IAAI,CAAO;IACzC,iBAAiB,EAAE,CAAC,MAAM,EAAE,eAAe,KAAK,IAAI,CAAO;IAC3D,GAAG,EAAE,MAAM,CAAI;IACf,cAAc,EAAE,KAAK,CAAA;IACrB,MAAM,EAAE,QAAQ,CAAO;IACvB,QAAQ,CAAC,EAAE,QAAQ,CAAA;IACnB,MAAM,EAAE,QAAQ,CAAA;IAChB,MAAM,EAAE,QAAQ,CAAA;IAChB,gBAAgB,EAAE,QAAQ,CAAA;IAC1B,IAAI,EAAE,aAAa,GAAG,IAAI,CAAO;IACjC,UAAU,EAAE,QAAQ,EAAE,CAAK;IAC3B,UAAU,EAAE,UAAU,CAAmB;IACzC,oBAAoB,EAAE;QACpB,IAAI,EAAE,QAAQ,EAAE,CAAA;QAChB,KAAK,EAAE,QAAQ,EAAE,CAAA;QACjB,KAAK,EAAE,QAAQ,EAAE,CAAA;QACjB,OAAO,EAAE,QAAQ,EAAE,CAAA;KACpB,CAKA;IACD,KAAK,EAAE,KAAK,CAAA;IACZ,WAAW,EAAE,CAAC,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAO;IACzD,MAAM,CAAC,EAAE,OAAO,CAAA;IAChB,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,SAAS,CAAC,EAAE,MAAM,CAAA;IAElB;;;;;;;;;;;;;;;;;OAiBG;gBACS,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,qBAAqB;IAuD7D;;OAEG;IACH,OAAO,IAAI,IAAI;IA4Bf;;;OAGG;IACH,WAAW,IAAI,MAAM;IAOrB;;;;;OAKG;IACH,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAiBhD;;OAEG;IACH,WAAW,IAAI,eAAe,EAAE;IAIhC;;;OAGG;IACG,aAAa,CACjB,OAAO,EAAE,eAAe,GACvB,OAAO,CAAC,6BAA6B,CAAC;IAWzC;;OAEG;IACG,iBAAiB,IAAI,OAAO,CAAC,6BAA6B,EAAE,CAAC;IASnE;;;;OAIG;IACH,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG;IAIzC;;OAEG;IACH,eAAe,IAAI,gBAAgB;IAanC;;OAEG;IACH,WAAW,IAAI,OAAO;IAItB,OAAO,CACL,KAAK,EAAE,MAAM,EACb,MAAM,GAAE,sBAAuC,GAC9C,eAAe;IAgBlB;;;;OAIG;IACH,IAAI,CAAC,IAAI,EAAE,eAAe,GAAG,IAAI;IAejC;;;;;;;;OAQG;IACG,OAAO,CAAC,KAAK,GAAE,MAAM,GAAG,IAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IAuBzD;;OAEG;IACG,aAAa;IA0BnB,WAAW,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI;IAG9D;;OAEG;IACH,eAAe;IA4Lf,OAAO,CAAC,gBAAgB;CAUzB;AAED,cAAM,gBAAgB;IACpB,UAAU,EAAE,MAAM,CAAgB;IAClC,KAAK,EAAE,QAAQ,CAAA;IACf,OAAO,EAAE,QAAQ,CAAW;IAC5B,OAAO,EAAE,QAAQ,CAAW;IAC5B,SAAS,EAAE,QAAQ,CAAW;IAC9B,MAAM,EAAE,QAAQ,CAAW;IAC3B,UAAU,EAAE,MAAM,CAA2B;IAC7C,IAAI,EAAE,QAAQ,CAAW;IACzB,GAAG,EAAE,MAAM,GAAG,GAAG,GAAG,IAAI,CAAO;gBAG7B,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,SAAS,EACrB,OAAO,EAAE;QAAE,KAAK,EAAE,QAAQ,CAAA;KAAE;CAK/B"}