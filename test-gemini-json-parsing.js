/**
 * Direct test of Gemini JSON parsing logic
 */

const GeminiService = require('./server/services/geminiService');

// Mock the problematic JSON response from your error log
const problematicJson = `{
  "title": "Acid-Base Reactions: Understanding pH and Neutralisation",
  "description": "This topic explores the concepts of acids and bases, their properties", the pH scale", and the process of neutralisation reactions.  It also covers different types of acids and bases and their applications.",
  "content": "## Acid-Base Reactions: Understanding pH and Neutralisation\\n\\nThis topic delves into the fascinating world of acids and bases, exploring their properties, reactions, and applications.  We'll cover key concepts like pH, indicators, neutralisation, and different types of acids and bases.\\n\\n### 1. Defining Acids and Bases\\n\\nAcids and bases are defined in several ways, the most common being the Arrhenius and Brønsted-Lowry definitions:\\n\\n* **Arrhenius Definition:**  An acid is a substance that produces hydrogen ions (H+) when dissolved in water, while a base is a substance that produces hydroxide ions (OH-) when dissolved in water.\\n* **Brønsted-Lowry Definition:** A broader definition, it defines an acid as a proton (H+) donor and a base as a proton acceptor. This definition extends beyond aqueous solutions.\\n\\nExamples of common acids include hydrochloric acid (HCl), sulfuric acid (H2SO4), and nitric acid (HNO3). Common bases include sodium hydroxide (NaOH), potassium hydroxide (KOH)", and calcium hydroxide Ca(OH)2.\\n\\n### 2. The pH Scale\\nThe pH scale is a logarithmic scale used to measure the acidity or alkalinity of a solution. It ranges from 0 to 14", "with":\\n\\n* pH 7: Neutral (pure water)\\n* pH < 7: Acidic (higher H+ concentration)\\n* pH > 7: Alkaline/Basic (higher OH- concentration)\\n\\nEach whole number change on the pH scale represents a tenfold change in H+ concentration. For example, a solution with a pH of 3 is ten times more acidic than a solution with a pH of 4.\\n\\n### 3. Indicators\\n\\nIndicators are substances that change color depending on the pH of a solution.  This color change allows us to determine whether a solution is acidic, neutral, or alkaline.  Common indicators include:\\n\\n* **Litmus paper:** Turns red in acidic solutions and blue in alkaline solutions.\\n* **Methyl orange:** Changes from red (acidic) to yellow (alkaline) around pH 4.4.\\n* **Phenolphthalein:** Changes from colorless (acidic) to pink (alkaline) around pH 8.3.\\n\\n### 4. Neutralisation Reactions\\n\\nNeutralisation is the reaction between an acid and a base. The products of a neutralisation reaction are usually salt and water.  For example:\\n\\nHCl(aq) + NaOH(aq) → NaCl(aq) + H2O(l)\\n\\nThis reaction shows hydrochloric acid reacting with sodium hydroxide to produce sodium chloride (salt) and water.  The heat released during this reaction is called the enthalpy change of neutralisation.\\n\\n### 5. Types of Acids and Bases\\n\\nAcids and bases can be classified as strong or weak, depending on their degree of dissociation in water:\\n\\n* **Strong acids:** Completely dissociate in water, meaning all the acid molecules break apart into ions. Examples include HCl, HNO3, and H2SO4.\\n* **Weak acids:** Only partially dissociate in water, meaning only some of the acid molecules break apart into ions. Examples include ethanoic acid (CH3COOH) and carbonic acid (H2CO3).\\n* **Strong bases:** Completely dissociate in water. Examples include NaOH and KOH.\\n* **Weak bases:** Only partially dissociate in water.  Examples include ammonia (NH3).\\n\\n### 6. Applications of Acids and Bases\\nAcids and bases have numerous applications in everyday life and industry, "including":\\n\\n* **Food and drinks:** Citric acid in citrus fruits, acetic acid in vinegar.\\n* **Cleaning products:** Many cleaning agents are either acidic or basic.\\n* **Industrial processes:** Acids and bases are used in the production of many chemicals and materials.\\n* **Medicine:**  Antacids (bases) neutralise excess stomach acid.\\nUnderstanding acid-base reactions is crucial in various fields, from chemistry and biology to medicine and environmental science.",
  "difficulty_level": 3,
  "estimated_study_time_minutes": 120,
  "learning_objectives": [
    "Define acids and bases using Arrhenius and Brønsted-Lowry definitions",
    "Understand the pH scale and its significance",
    "Identify common indicators and their color changes",
    "Explain neutralisation reactions and their products"
  ]
}`;

async function testJsonParsing() {
  console.log('🧪 Testing Gemini JSON parsing fix...\n');

  try {
    // Create a mock service with just the parsing methods
    const service = {
      _cleanupJsonString: require('./server/services/geminiService').prototype._cleanupJsonString,
      _applyJsonFixes: require('./server/services/geminiService').prototype._applyJsonFixes
    };
    
    console.log('📝 Testing with problematic JSON from error log...');
    console.log('Raw JSON length:', problematicJson.length, 'characters');
    console.log('First 200 chars:', problematicJson.substring(0, 200));
    console.log('');
    
    // Test the cleanup function directly
    const cleanedJson = service._cleanupJsonString(problematicJson);
    
    console.log('✅ JSON cleanup completed successfully!');
    console.log('Cleaned JSON length:', cleanedJson.length, 'characters');
    
    // Try to parse the cleaned JSON
    const parsed = JSON.parse(cleanedJson);
    
    console.log('✅ JSON parsing successful!');
    console.log('📋 Parsed object structure:');
    console.log('- Title:', parsed.title);
    console.log('- Description length:', parsed.description?.length || 0, 'chars');
    console.log('- Content length:', parsed.content?.length || 0, 'chars');
    console.log('- Difficulty level:', parsed.difficulty_level);
    console.log('- Study time:', parsed.estimated_study_time_minutes, 'minutes');
    console.log('- Learning objectives:', parsed.learning_objectives?.length || 0, 'items');
    
    console.log('\n🎉 SUCCESS: The JSON parsing fix works correctly!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error details:', error);
    return false;
  }
}

// Test with a simpler problematic case
async function testSimpleCase() {
  console.log('\n🔧 Testing with simpler problematic JSON...\n');
  
  const simpleProblematicJson = `{
    "title": "Test Title",
    "description": "A description with "quoted" text",
    "content": "Some content with, issues"
  }`;
  
  try {
    const service = {
      _cleanupJsonString: require('./server/services/geminiService').prototype._cleanupJsonString,
      _applyJsonFixes: require('./server/services/geminiService').prototype._applyJsonFixes
    };
    const cleaned = service._cleanupJsonString(simpleProblematicJson);
    const parsed = JSON.parse(cleaned);
    
    console.log('✅ Simple case also works!');
    console.log('Parsed:', JSON.stringify(parsed, null, 2));
    return true;
    
  } catch (error) {
    console.error('❌ Simple case failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting Gemini JSON parsing tests...\n');
  
  const test1 = await testJsonParsing();
  const test2 = await testSimpleCase();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS:');
  console.log('- Complex JSON fix:', test1 ? '✅ PASS' : '❌ FAIL');
  console.log('- Simple JSON fix:', test2 ? '✅ PASS' : '❌ FAIL');
  
  if (test1 && test2) {
    console.log('\n🎉 All tests passed! The Gemini JSON parsing fix is working correctly.');
    process.exit(0);
  } else {
    console.log('\n💥 Some tests failed. The fix needs more work.');
    process.exit(1);
  }
}

main().catch(console.error);
