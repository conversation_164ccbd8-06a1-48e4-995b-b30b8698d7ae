import { useState } from 'react';
import { llm<PERSON>ervice, LLMProvider, LLMServiceOptions } from '../services/llmService';
import { supabase } from '../lib/supabase';
import { Subject } from './useSubjects';
import { Topic } from './useTopics';
import { Flashcard } from './useFlashcards';
import { useAuth } from '../contexts/AuthContext';
import { validateChemistryContent, ValidationResult, isChemistryContent as isChemContent } from '../utils/chemistryValidator';

// --- Interfaces ---

export interface GeneratedSubject {
  name: string;
  code: string;
  description: string;
  color_hex: string;
  icon_name: string;
}

export interface GeneratedTopic {
  title: string;
  description: string;
  content: string;
  difficulty_level: number;
  estimated_study_time_minutes: number;
  learning_objectives: string[];
}

export interface QuizQuestion {
  question_text: string;
  options: string[];
  correct_answer_index: number;
  explanation: string;
  difficulty_level: number;
}

export interface GeneratedQuiz {
  title: string;
  description: string;
  difficulty_level: number;
  time_limit_minutes: number;
  questions: QuizQuestion[];
}

// --- Helper Functions ---

const isChemistryContent = (subjectName: string): boolean => {
  if (!subjectName) return false;
  const normalizedSubject = subjectName.toLowerCase().trim();
  return normalizedSubject.includes('chemistry') ||
         normalizedSubject.includes('chemical') ||
         normalizedSubject === 'chem';
};


// --- Custom Hooks ---

/**
 * Hook for generating a list of topics for a subject.
 */
export function useTopicListGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useAuth();

  const generateTopicList = async (
    subjectName: string,
    gradeLevel: string,
    provider: LLMProvider,
    model: string
  ): Promise<Partial<Topic>[] | null> => {
    try {
      setLoading(true);
      setError(null);

      if (!session?.access_token) {
        throw new Error('Authentication required.');
      }

      const systemPrompt = `
        You are an expert IGCSE curriculum designer.
        For the subject "${subjectName}" at grade level(s) "${gradeLevel}", generate a comprehensive list of 10-15 essential topics.
        For each topic, provide a title and a brief, one-sentence description.
        Return a JSON array of objects, where each object has "title" and "description" keys.
        Example: [{"title": "Cell Structure", "description": "Understanding the components of animal and plant cells."}]
      `;

      const response = await llmService.generateContent(systemPrompt, {
        authToken: session.access_token,
        provider,
        model,
        temperature: 0.5,
      });

      if (!response) {
        throw new Error('LLM service returned no response.');
      }

      const startIndex = response.indexOf('[');
      const endIndex = response.lastIndexOf(']');
      if (startIndex === -1 || endIndex === -1) {
        throw new Error('No JSON array found in the LLM response.');
      }
      const jsonString = response.substring(startIndex, endIndex + 1);
      const parsedTopics = JSON.parse(jsonString);

      if (!Array.isArray(parsedTopics)) {
        throw new Error('LLM response is not a JSON array.');
      }

      return parsedTopics.filter(t => t.title && t.description);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate topic list';
      setError(errorMessage);
      console.error(errorMessage, err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateTopicList, loading, error };
}


/**
 * Hook for generating the detailed content of a single topic.
 */
export function useTopicContentGeneration() {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [validationResults, setValidationResults] = useState<ValidationResult | null>(null);
    const { session } = useAuth();

    const generateTopicContent = async (
        subjectName: string,
        topicName: string,
        gradeLevel: string,
        provider: LLMProvider,
        model: string
    ): Promise<Partial<Topic> | null> => {
        try {
            setLoading(true);
            setError(null);
            setValidationResults(null);

            if (!session || !session.access_token) {
                throw new Error('Authentication required.');
            }

            let instruction = `For the subject "${subjectName}", generate a comprehensive topic breakdown for the topic "${topicName}".`;
            if (gradeLevel && gradeLevel.trim() !== '') {
                instruction += ` This content should be tailored for IGCSE grade levels ${gradeLevel}.`;
            }
            if (isChemistryContent(subjectName)) {
                instruction += ` The subject is chemistry, so ensure all chemical formulas, equations, and terminology are accurate and correctly formatted.`;
            }

            const systemPrompt = `
              ${instruction}
              Return a single JSON object with these fields:
              - title: The title of the topic (use "${topicName}" or improve it)
              - description: A brief description of the topic (1-2 sentences)
              - content: Detailed educational content for this topic (at least 500 words, with markdown formatting, including headings, lists, and bold text for key terms).
              - difficulty_level: A number from 1-5 representing difficulty (1=easiest, 5=hardest)
              - estimated_study_time_minutes: Estimated time to study this topic in minutes (e.g., 45)
              - learning_objectives: An array of 3-5 learning objectives for this topic
            `;

            const result = await llmService.generateJSON<Partial<Topic>>(systemPrompt, {
                authToken: session.access_token,
                provider: provider,
                model: model,
            });

            if (result && result.content && isChemistryContent(subjectName)) {
                console.log('Validating Chemistry topic content...');
                const validation = validateChemistryContent(result.content);
                setValidationResults(validation);
                if (validation.warnings.length > 0 || validation.errors.length > 0) {
                    console.warn('Chemistry content validation issues:', validation);
                }
            }

            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to generate topic content';
            setError(errorMessage);
            console.error(errorMessage, err);
            return null;
        } finally {
            setLoading(false);
        }
    };

    return { generateTopicContent, loading, error, validationResults };
}

/**
 * Hook for generating and saving flashcards using an LLM.
 */
export function useLLMFlashcardGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationResults, setValidationResults] = useState<ValidationResult | null>(null);
  const { session } = useAuth();

  const generateFlashcards = async (
    subjectName: string,
    topicTitle: string,
    count: number,
    difficulty: string,
    provider: LLMProvider,
    model: string
  ): Promise<Partial<Flashcard>[] | null> => {
    try {
      setLoading(true);
      setError(null);
      setValidationResults(null);

      if (!session?.access_token) {
        throw new Error('Authentication required. Please log in.');
      }

      const prompt = `You are an expert in creating educational content for the IGCSE curriculum. Your task is to generate a set of ${count} high-quality flashcards for the topic \"${topicTitle}\" in the subject \"${subjectName}\". The target audience is IGCSE students, and the difficulty level should be ${difficulty}.

Each flashcard must have a 'front_content' (a question, term, or concept) and a 'back_content' (a concise and accurate answer or explanation).

Return the output as a JSON array of objects. For example:
[
  {
    "front_content": "What is the chemical symbol for Gold?",
    "back_content": "Au"
  },
  {
    "front_content": "Define 'isotope'.",
    "back_content": "Atoms of the same element that have the same number of protons but a different number of neutrons."
  }
]`;

      const response = await llmService.generateContent(prompt, {
        authToken: session.access_token,
        provider,
        model,
        temperature: 0.7,
        maxTokens: 150 * count,
      });

      if (!response) {
        throw new Error('LLM service returned no response.');
      }
      
      const startIndex = response.indexOf('[');
      const endIndex = response.lastIndexOf(']');
      if (startIndex === -1 || endIndex === -1) {
        throw new Error('No JSON array found in the LLM response.');
      }
      const jsonString = response.substring(startIndex, endIndex + 1);
      const parsedFlashcards = JSON.parse(jsonString);

      if (!Array.isArray(parsedFlashcards)) {
        throw new Error('LLM response is not a JSON array.');
      }

      const validFlashcards = parsedFlashcards.filter(fc => fc.front_content && fc.back_content);

      if (isChemistryContent(subjectName)) {
        const allContent = validFlashcards.map(fc => `${fc.front_content}\n${fc.back_content}`).join('\n\n');
        const validation = validateChemistryContent(allContent);
        setValidationResults(validation);
        if (validation.warnings.length > 0 || validation.errors.length > 0) {
          console.warn('Chemistry content validation issues:', validation);
        }
      }

      return validFlashcards;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate flashcards';
      setError(errorMessage);
      console.error('Error generating or parsing flashcards:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateFlashcards, loading, error, validationResults };
}


/**
 * Hook for generating quizzes using an LLM.
 */
export function useQuizGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationResults, setValidationResults] = useState<ValidationResult | null>(null);
  const { session } = useAuth();

    const generateQuiz = async (
    topicTitle: string,
    topicContent: string,
    questionCount: number,
    difficulty: string,
    provider: LLMProvider,
    model: string
  ): Promise<GeneratedQuiz | null> => {
    try {
      setLoading(true);
      setError(null);
      setValidationResults(null);

      if (!session?.access_token) {
        throw new Error('Authentication required. Please log in to generate a quiz.');
      }

      const systemPrompt = `
                You are an expert IGCSE quiz creator. Based on the topic \"${topicTitle}\" and its content, generate a quiz with ${questionCount} multiple-choice questions with a difficulty level of '${difficulty}'.
        The quiz should assess understanding of the key concepts in the provided content.
        
        Return a single JSON object with these fields:
        - title: A suitable title for the quiz (e.g., \"Quiz: ${topicTitle}\")
        - description: A brief description of what the quiz covers.
        - difficulty_level: A number from 1-5 representing the overall difficulty.
        - time_limit_minutes: An estimated time limit in minutes.
        - questions: An array of question objects. Each question object must have:
          - question_text: The text of the question.
          - options: An array of 4 strings representing the possible answers.
          - correct_answer_index: The 0-based index of the correct answer in the options array.
          - explanation: A brief explanation for the correct answer.
          - difficulty_level: A number from 1-5 representing difficulty.
      `;

      const result = await llmService.generateJSON<GeneratedQuiz>(systemPrompt, {
        maxTokens: 3000,
        authToken: session.access_token,
        provider: provider,
        model: model,
      });

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate quiz';
      setError(errorMessage);
      console.error('Error generating quiz:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { generateQuiz, loading, error, validationResults };
}