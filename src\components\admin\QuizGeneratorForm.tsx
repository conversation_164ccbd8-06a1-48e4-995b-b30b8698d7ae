import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Chip, FormControl, InputLabel, Select, MenuItem, TextField } from '@mui/material';
import LLMProviderSelector from './LLMProviderSelector';
import { Send as SendIcon, AutoFixHigh as AutoFixHighIcon } from '@mui/icons-material';
import { useQuizGeneration } from '../../hooks/useQuizGeneration';
import { useSubjects } from '../../hooks/useSubjects';
import { useTopics } from '../../hooks/useTopics';
import { LLMProvider } from '../../services/llmAdapter';
import { isChemistryContent, ValidationResult } from '../../utils/chemistryValidator';
import ChemistryValidationResults from '../validation/ChemistryValidationResults';
import { useReview } from '../../contexts/ReviewContext';
import { ContentType, ReviewState } from '../../types/reviewTypes';

interface QuizGeneratorFormProps {
  subjects: any[];
  topics: any[];
  onSubjectChange: (subjectId: string | null) => void;
}

const QuizGeneratorForm: React.FC<QuizGeneratorFormProps> = ({ 
  subjects, 
  topics, 
  onSubjectChange 
}) => {
  const { subjects: allSubjects } = useSubjects();
  const [selectedSubjectId, setSelectedSubjectId] = useState<string>('');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [questionCount, setQuestionCount] = useState<number>(5);
  const [difficultyLevel, setDifficultyLevel] = useState<string>('medium');
    const [llmProvider, setLlmProvider] = useState<LLMProvider>(LLMProvider.OPENAI);
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');
  const [isChemistry, setIsChemistry] = useState<boolean>(false);
  const [generatedQuiz, setGeneratedQuiz] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { topics: filteredTopics, loading: loadingTopics } = useTopics(selectedSubjectId);
  const { generateAndSaveQuiz, loading, error, validationResults } = useQuizGeneration();
  const { submitForReview, getContentReviewState } = useReview();
  const [reviewState, setReviewState] = useState<ReviewState | null>(null);

  // Check if the selected subject or topic is chemistry-related
  useEffect(() => {
    if (selectedSubjectId && selectedTopic) {
      const subjectName = allSubjects.find(s => s.id === selectedSubjectId)?.name || '';
      const topicTitle = filteredTopics.find(t => t.id === selectedTopic)?.title || '';
      const isChemistrySubject = isChemistryContent(subjectName);
      const isChemistryTopic = isChemistryContent(topicTitle);
      setIsChemistry(isChemistrySubject || isChemistryTopic);
    } else {
      setIsChemistry(false);
    }
  }, [selectedSubjectId, selectedTopic, allSubjects, filteredTopics]);

  // Fetch review state when we have a generated quiz
  useEffect(() => {
    if (generatedQuiz?.id) {
      const fetchReviewState = async () => {
        try {
          const state = await getContentReviewState(ContentType.QUIZ, generatedQuiz.id);
          setReviewState(state);
        } catch (error) {
          console.error('Error fetching review state:', error);
        }
      };
      
      fetchReviewState();
    } else {
      setReviewState(null);
    }
  }, [generatedQuiz?.id, getContentReviewState]);

  const handleGenerate = async () => {
    if (!selectedTopic || !selectedSubjectId) {
      alert('Please select a subject and a topic first.');
      return;
    }

    try {
            const topic = filteredTopics.find(t => t.id === selectedTopic);
      if (!topic || !topic.title) {
        alert('Selected topic details could not be found.');
        return;
      }

      const newQuiz = await generateAndSaveQuiz(
        selectedTopic, // topicId
        topic.title,
        topic.content || '',
        questionCount,
        difficultyLevel,
        llmProvider,
        selectedModel
      );

      if (newQuiz) {
        setGeneratedQuiz(newQuiz);
        alert(`Successfully generated quiz with ${questionCount} questions for ${selectedTopic}`);
      }
    } catch (err) {
      console.error('Failed to generate quiz:', err);
    }
  };

  const handleSubmitForReview = async () => {
    if (!generatedQuiz || !generatedQuiz.id) {
      alert('Please generate and save a quiz before submitting for review.');
      return;
    }

    setIsSubmitting(true);
    try {
      await submitForReview(
        ContentType.QUIZ,
        generatedQuiz.id
      );
      
      // Update local state after submission
      setReviewState(ReviewState.PENDING_REVIEW);
      alert('Quiz submitted for review successfully!');
    } catch (error) {
      console.error('Error submitting for review:', error);
      alert('Failed to submit quiz for review.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to render review status badge
  const renderReviewStatusBadge = () => {
    if (!reviewState) return null;

    let color: 'warning' | 'success' | 'error' | 'info' | 'default' = 'default';
    let label = 'Unknown';
    
    switch (reviewState) {
      case ReviewState.DRAFT:
        color = 'default';
        label = 'Draft';
        break;
      case ReviewState.PENDING_REVIEW:
        color = 'warning';
        label = 'Pending Review';
        break;
      case ReviewState.APPROVED:
        color = 'success';
        label = 'Approved';
        break;
      case ReviewState.REJECTED:
        color = 'error';
        label = 'Rejected';
        break;
      case ReviewState.NEEDS_REVISION:
        color = 'info';
        label = 'Needs Revision';
        break;
    }
    
    return <Chip label={label} color={color} size="small" sx={{ ml: 1 }} />;
  };

  return (
    <div>
      {/* Header with title and review badge */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Generate Quiz {renderReviewStatusBadge()}
        </Typography>
        <Box>
          {/* Submit for Review Button */}
          {generatedQuiz?.id && (
            <Button 
              variant="outlined" 
              color="secondary"
              onClick={handleSubmitForReview}
              disabled={isSubmitting || loading || reviewState === ReviewState.PENDING_REVIEW || reviewState === ReviewState.APPROVED}
              startIcon={<SendIcon />}
              sx={{ mr: 1 }}
            >
              Submit for Review
            </Button>
          )}
          <Button 
            variant="contained"
            onClick={handleGenerate}
            disabled={!selectedTopic || !selectedSubjectId || loading || isSubmitting}
            startIcon={<AutoFixHighIcon />}
          >
            {loading || isSubmitting ? 'Generating...' : 'Generate'}
          </Button>
        </Box>
      </Box>

      {/* Input form */}
      <form className="mb-6">
        {/* LLM Provider Selection */}
        <div className="mb-6">
                    <LLMProviderSelector
            selectedProvider={llmProvider}
            selectedModel={selectedModel}
            onProviderChange={setLlmProvider}
            onModelChange={setSelectedModel}
            disabled={loading || isSubmitting}
          />
        </div>

        <div className="mb-4">
          <FormControl fullWidth>
            <InputLabel id="subject-label">Select Subject</InputLabel>
            <Select
              labelId="subject-label"
              id="subject"
              value={selectedSubjectId}
              label="Select Subject"
              onChange={(e) => {
                setSelectedSubjectId(e.target.value);
                onSubjectChange(e.target.value);
              }}
              disabled={loading || isSubmitting}
            >
              {allSubjects.map(subject => (
                <MenuItem key={subject.id} value={subject.id}>
                  {subject.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>
        
        <div className="mb-4">
          <FormControl fullWidth>
            <InputLabel id="topic-label">Select Topic</InputLabel>
            <Select
              labelId="topic-label"
              id="topic"
              value={selectedTopic}
              label="Select Topic"
              onChange={(e) => setSelectedTopic(e.target.value)}
              disabled={loading || isSubmitting || filteredTopics.length === 0}
            >
              {filteredTopics.map(topic => (
                <MenuItem key={topic.id} value={topic.id}>
                  {topic.title}
                </MenuItem>
              ))}
            </Select>
            {filteredTopics.length === 0 && (
              <p className="mt-1 text-sm text-red-600">
                No topics available for this subject. Please create topics first.
              </p>
            )}
          </FormControl>
        </div>
        
        <div className="mb-4">
          <TextField
            id="question-count"
            label="Number of Questions"
            type="number"
            value={questionCount}
            onChange={(e) => setQuestionCount(parseInt(e.target.value))}
            disabled={loading || isSubmitting}
            fullWidth
          />
        </div>
        
        <div className="mb-4">
          <FormControl fullWidth>
            <InputLabel id="difficulty-level-label">Difficulty Level</InputLabel>
            <Select
              labelId="difficulty-level-label"
              id="difficulty-level"
              value={difficultyLevel}
              label="Difficulty Level"
              onChange={(e) => setDifficultyLevel(e.target.value)}
              disabled={loading || isSubmitting}
            >
              <MenuItem value="easy">Easy</MenuItem>
              <MenuItem value="medium">Medium</MenuItem>
              <MenuItem value="hard">Hard</MenuItem>
            </Select>
          </FormControl>
        </div>
      </form>
      
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      {/* Chemistry validation results */}
      {isChemistry && (
        <ChemistryValidationResults 
          validationResults={validationResults}
          onDismiss={() => {}} 
        />
      )}
    </div>
  );
};

export default QuizGeneratorForm;
