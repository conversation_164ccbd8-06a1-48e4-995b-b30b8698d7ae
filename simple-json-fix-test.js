/**
 * Simple test to understand and fix the exact JSON issue
 */

// The exact problematic pattern from the logs
const problematicJson = `{
  "title": "Acid-Base Reactions: Understanding pH and Neutralisation",
  "description": "This topic explores the concepts of acids and bases, their properties", the pH scale", and the process of neutralisation reactions.  It also covers different types of acids and bases and their applications.",
  "content": "Some content here"
}`;

console.log('Original problematic JSON:');
console.log(problematicJson);
console.log('\n' + '='.repeat(60) + '\n');

function simpleJsonFix(jsonString) {
  let fixed = jsonString;

  console.log('Step 1: Fix broken string values within property values only');
  // The issue: "description": "text", "more text" -> "description": "text, more text"
  // But we need to be careful not to merge across property boundaries

  // Look for pattern: ": "text", "more text" and fix it
  fixed = fixed.replace(/(:\s*"[^"]*?"),\s*"([^"]*?")/g, '$1, $2');
  console.log('After step 1:', fixed.substring(0, 200));

  console.log('\nStep 2: Fix specific broken patterns');
  // Fix: ", the pH scale" -> ", the pH scale"
  fixed = fixed.replace(/",\s*the\s+([^"]*?)"/g, ', the $1"');
  fixed = fixed.replace(/",\s*and\s+([^"]*?)"/g, ', and $1"');
  console.log('After step 2:', fixed.substring(0, 200));

  console.log('\nStep 3: Fix property names missing opening quotes');
  // Fix: , description": -> , "description":
  fixed = fixed.replace(/,\s*([a-zA-Z_][a-zA-Z0-9_]*"):/g, ', "$1:');
  console.log('After step 3:', fixed.substring(0, 200));

  console.log('\nStep 4: Remove trailing commas');
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');
  console.log('After step 4:', fixed.substring(0, 200));

  return fixed;
}

try {
  console.log('Testing simple JSON fix...');
  const fixed = simpleJsonFix(problematicJson);
  
  console.log('\nFinal fixed JSON:');
  console.log(fixed);
  
  console.log('\nTrying to parse...');
  const parsed = JSON.parse(fixed);
  
  console.log('✅ SUCCESS! JSON parsed correctly');
  console.log('Parsed object keys:', Object.keys(parsed));
  
} catch (error) {
  console.error('❌ Still failed:', error.message);
  console.error('Error position:', error.message.match(/position (\d+)/)?.[1]);
}
