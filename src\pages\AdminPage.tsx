import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSubjects } from '../hooks/useSubjects';
import { useTopics } from '../hooks/useTopics';
import SubjectGeneratorForm from '../components/admin/SubjectGeneratorForm';
import TopicGeneratorForm from '../components/admin/TopicGeneratorForm';
import FlashcardGeneratorForm from '../components/admin/FlashcardGeneratorForm';
import QuizGeneratorForm from '../components/admin/QuizGeneratorForm';
import ExamPaperGeneratorForm from '../components/admin/ExamPaperGeneratorForm';
import LLMProviderTester from '../components/admin/LLMProviderTester';

/**
 * Admin page for content generation using LLM
 */
const AdminPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'subjects' | 'topics' | 'flashcards' | 'quizzes' | 'exam-papers' | 'test-llm'>('subjects');
  const { subjects } = useSubjects();
  const navigate = useNavigate();

  // Get topics for the selected subject
  const [selectedSubjectId, setSelectedSubjectId] = useState<string | null>(null);
  const { topics } = useTopics(selectedSubjectId);

  // Handle tab change
  const handleTabChange = (tab: 'subjects' | 'topics' | 'flashcards' | 'quizzes' | 'exam-papers' | 'test-llm') => {
    setActiveTab(tab);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Content Generation Admin</h1>
        <button 
          onClick={() => navigate('/dashboard')}
          className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6 overflow-x-auto">
        <button
          className={`px-6 py-3 font-medium text-sm ${
            activeTab === 'subjects'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => handleTabChange('subjects')}
        >
          Subjects
        </button>
        <button
          className={`px-6 py-3 font-medium text-sm ${
            activeTab === 'topics'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => handleTabChange('topics')}
        >
          Topics
        </button>
        <button
          className={`px-6 py-3 font-medium text-sm ${
            activeTab === 'flashcards'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => handleTabChange('flashcards')}
        >
          Flashcards
        </button>
        <button
          className={`px-6 py-3 font-medium text-sm ${
            activeTab === 'quizzes'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => handleTabChange('quizzes')}
        >
          Quizzes
        </button>
        <button
          className={`px-6 py-3 font-medium text-sm ${
            activeTab === 'exam-papers'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => handleTabChange('exam-papers')}
        >
          Exam Papers
        </button>
        <button
          className={`px-6 py-3 font-medium text-sm ${
            activeTab === 'test-llm'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => handleTabChange('test-llm')}
        >
          Test LLM Providers
        </button>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-md p-6">
        {activeTab === 'subjects' && (
          <SubjectGeneratorForm />
        )}
        
        {activeTab === 'topics' && (
          <TopicGeneratorForm 
            subjects={subjects} 
            onSubjectChange={setSelectedSubjectId} 
          />
        )}
        
        {activeTab === 'flashcards' && (
          <FlashcardGeneratorForm />
        )}
        
        {activeTab === 'quizzes' && (
          <QuizGeneratorForm 
            subjects={subjects} 
            topics={topics} 
            onSubjectChange={setSelectedSubjectId} 
          />
        )}

        {activeTab === 'exam-papers' && (
          <ExamPaperGeneratorForm />
        )}
        
        {activeTab === 'test-llm' && (
          <LLMProviderTester />
        )}
      </div>
    </div>
  );
};

export default AdminPage;
