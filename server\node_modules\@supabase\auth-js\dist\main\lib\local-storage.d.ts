import { SupportedStorage } from './types';
/**
 * Provides safe access to the globalThis.localStorage property.
 */
export declare const localStorageAdapter: SupportedStorage;
/**
 * Returns a localStorage-like object that stores the key-value pairs in
 * memory.
 */
export declare function memoryLocalStorageAdapter(store?: {
    [key: string]: string;
}): SupportedStorage;
//# sourceMappingURL=local-storage.d.ts.map