/**
 * Test script to verify the Gemini JSON parsing fix
 */

const https = require('https');
const http = require('http');

function makeHttpRequest(url, options) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const protocol = urlObj.protocol === 'https:' ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = protocol.request(requestOptions, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            const result = JSON.parse(data);
            resolve(result);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testGeminiJsonGeneration() {
  try {
    console.log('🧪 Testing Gemini JSON generation fix...');
    
    const testPayload = {
      prompt: `Generate a topic for IGCSE Chemistry about "Acids and Bases". Include:
- title: A clear topic title
- description: Brief description (2-3 sentences)
- content: Detailed content with markdown formatting
- difficulty_level: Number from 1-5
- estimated_study_time_minutes: Number
- learning_objectives: Array of 3-4 learning objectives`,
      model: 'gemini-1.5-flash',
      provider: 'google',
      temperature: 0.7,
      maxTokens: 2000
    };

    console.log('📤 Sending request to Gemini API...');
    
    const result = await makeHttpRequest('http://localhost:3001/api/llm/generate-json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log('✅ Success! Gemini JSON generation is working');
    console.log('📋 Generated topic structure:');
    console.log('- Title:', result.title);
    console.log('- Description length:', result.description?.length || 0, 'chars');
    console.log('- Content length:', result.content?.length || 0, 'chars');
    console.log('- Difficulty level:', result.difficulty_level);
    console.log('- Study time:', result.estimated_study_time_minutes, 'minutes');
    console.log('- Learning objectives:', result.learning_objectives?.length || 0, 'items');
    
    // Validate the structure
    const requiredFields = ['title', 'description', 'content', 'difficulty_level', 'estimated_study_time_minutes', 'learning_objectives'];
    const missingFields = requiredFields.filter(field => !result[field]);
    
    if (missingFields.length > 0) {
      console.log('⚠️  Missing fields:', missingFields);
    } else {
      console.log('✅ All required fields present');
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('JSON')) {
      console.log('💡 This indicates the JSON parsing fix may need further refinement');
    } else if (error.message.includes('GOOGLE_API_KEY')) {
      console.log('💡 Please ensure GOOGLE_API_KEY is properly configured in server/.env');
    } else if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 Please ensure the server is running on port 3001');
    }
    
    return null;
  }
}

// Run the test
if (require.main === module) {
  testGeminiJsonGeneration()
    .then(result => {
      if (result) {
        console.log('\n🎉 Gemini JSON generation test completed successfully!');
        process.exit(0);
      } else {
        console.log('\n💥 Test failed - see errors above');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testGeminiJsonGeneration };
