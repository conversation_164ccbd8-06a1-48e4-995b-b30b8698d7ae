import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Chip, TextField, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert } from '@mui/material';
import { Send as SendIcon, AutoFixHigh as AutoFixHighIcon } from '@mui/icons-material';
import { useFlashcardGeneration } from '../../hooks/useFlashcardGeneration';
import { Flashcard } from '../../hooks/useFlashcards';
import { useSubjects } from '../../hooks/useSubjects';
import { useTopics } from '../../hooks/useTopics';
import LLMProviderSelector from './LLMProviderSelector';
import { LLMProvider } from '../../services/llmAdapter';

import { useReview } from '../../contexts/ReviewContext';
import { ContentType, ReviewState } from '../../types/reviewTypes';

const FlashcardGeneratorForm: React.FC = () => {
  const { subjects } = useSubjects();
  const [selectedSubjectId, setSelectedSubjectId] = useState<string>('');
  const { topics: filteredTopics, loading: topicsLoading } = useTopics(selectedSubjectId);
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [flashcardCount, setFlashcardCount] = useState<number>(5);

  const [llmProvider, setLlmProvider] = useState<LLMProvider>(LLMProvider.OPENAI);
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');
  const [generatedFlashcards, setGeneratedFlashcards] = useState<Partial<Flashcard>[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [difficulty, setDifficulty] = useState('medium');

  const { generateFlashcards, saveFlashcards, loading, error } = useFlashcardGeneration();
  const { submitForReview, getContentReviewState } = useReview();
  const [reviewState, setReviewState] = useState<ReviewState | null>(null);
  const [savedFlashcardSet, setSavedFlashcardSet] = useState<any>(null);

  const handleSubjectChange = (newSubjectId: string) => {
    setSelectedSubjectId(newSubjectId);
    setSelectedTopic(''); // Reset topic when subject changes to prevent stale state
  };

  useEffect(() => {
    if (subjects.length > 0 && !selectedSubjectId) {
      setSelectedSubjectId(subjects[0].id);
    }
  }, [subjects, selectedSubjectId]);

  useEffect(() => {
    // When the subject changes, filteredTopics will update.
    // Reset the selected topic.
    if (filteredTopics.length > 0) {
      setSelectedTopic(filteredTopics[0].id);
    } else {
      setSelectedTopic('');
    }
  }, [filteredTopics]);

  useEffect(() => {
    if (savedFlashcardSet?.id) {
      const fetchReviewState = async () => {
        try {
          const state = await getContentReviewState(ContentType.FLASHCARD, savedFlashcardSet.id);
          setReviewState(state);
        } catch (error) {
          console.error('Error fetching review state:', error);
        }
      };
      
      fetchReviewState();
    } else {
      setReviewState(null);
    }
  }, [savedFlashcardSet, getContentReviewState]);

  const handleGenerate = async () => {
    if (!selectedTopic || !selectedSubjectId) {
      alert('Please select a subject and a topic first.');
      return;
    }

    const subject = subjects.find(s => s.id === selectedSubjectId);
    const topic = filteredTopics.find(t => t.id === selectedTopic);

    if (!subject || !topic) {
      alert('Selected subject or topic not found.');
      return;
    }

    const flashcards = await generateFlashcards(
      subject.name,
      topic.title,
      flashcardCount,
      difficulty,
      llmProvider,
      selectedModel
    );

    if (flashcards) {
      setGeneratedFlashcards(flashcards);
      setSavedFlashcardSet(null);
    }
  };

  const handleSave = async () => {
    if (!selectedTopic || generatedFlashcards.length === 0) {
      alert('Please generate flashcards before saving.');
      return;
    }

    const flashcardSet = await saveFlashcards(selectedTopic, difficulty, generatedFlashcards);

    if (flashcardSet) {
      alert('Flashcards saved successfully!');
      setSavedFlashcardSet(flashcardSet);
    }
  };

  const handleFlashcardChange = (index: number, field: 'front_content' | 'back_content', value: string) => {
    const updatedFlashcards = [...generatedFlashcards];
    updatedFlashcards[index] = { ...updatedFlashcards[index], [field]: value };
    setGeneratedFlashcards(updatedFlashcards);
  };

  const handleSubmitForReview = async () => {
    if (!savedFlashcardSet) {
      alert('Please save the flashcards before submitting for review.');
      return;
    }

    setIsSubmitting(true);
    try {
      await submitForReview(ContentType.FLASHCARD, savedFlashcardSet.id);

      const state = await getContentReviewState(ContentType.FLASHCARD, savedFlashcardSet.id);
      setReviewState(state);

      alert('Flashcards submitted for review successfully!');
    } catch (error) {
      console.error('Error submitting for review:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'An unknown error occurred'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderReviewStatusBadge = () => {
    if (!reviewState) return null;

    let color: 'warning' | 'success' | 'error' | 'info' | 'default' = 'default';
    let label = 'Unknown';
    
    switch (reviewState) {
      case ReviewState.DRAFT:
        color = 'default';
        label = 'Draft';
        break;
      case ReviewState.PENDING_REVIEW:
        color = 'warning';
        label = 'Pending Review';
        break;
      case ReviewState.APPROVED:
        color = 'success';
        label = 'Approved';
        break;
      case ReviewState.REJECTED:
        color = 'error';
        label = 'Rejected';
        break;
      case ReviewState.NEEDS_REVISION:
        color = 'info';
        label = 'Needs Revision';
        break;
    }
    
    return <Chip label={label} color={color} size="small" sx={{ ml: 1 }} />;
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Flashcard Generation</h2>
      
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">Controls</h3>
        <div>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={handleGenerate}
            disabled={loading}
            startIcon={<AutoFixHighIcon />}
          >
            {loading ? 'Generating...' : 'Generate Flashcards'}
          </Button>
          <Button 
            variant="contained" 
            color="secondary" 
            onClick={handleSubmitForReview}
            disabled={isSubmitting || !savedFlashcardSet || !!reviewState}
            startIcon={<SendIcon />}
            sx={{ ml: 2 }}
          >
            {isSubmitting ? 'Submitting...' : 'Submit for Review'}
          </Button>
        </div>
      </div>

      {renderReviewStatusBadge()}

      {error && <Alert severity="error" className="mb-4">{error}</Alert>}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <FormControl fullWidth>
          <InputLabel id="subject-select-label">Subject</InputLabel>
          <Select
            labelId="subject-select-label"
            id="subject-select"
            value={selectedSubjectId}
            label="Subject"
            onChange={(e) => handleSubjectChange(e.target.value as string)}
            disabled={loading}
          >
            {subjects.map((subject) => (
              <MenuItem key={subject.id} value={subject.id}>
                {subject.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl fullWidth disabled={topicsLoading || filteredTopics.length === 0}>
          <InputLabel id="topic-select-label">Topic</InputLabel>
          <Select
            labelId="topic-select-label"
            id="topic-select"
            value={selectedTopic}
            label="Topic"
            onChange={(e) => setSelectedTopic(e.target.value as string)}
          >
            {filteredTopics.map((topic) => (
              <MenuItem key={topic.id} value={topic.id}>
                {topic.title}
              </MenuItem>
            ))}
          </Select>
          {filteredTopics.length === 0 && !topicsLoading && <FormHelperText>No topics for this subject.</FormHelperText>}
          {topicsLoading && <FormHelperText>Loading topics...</FormHelperText>}
        </FormControl>

        <TextField
          id="flashcard-count"
          label="Number of Flashcards"
          type="number"
          value={flashcardCount}
          onChange={(e) => setFlashcardCount(Number(e.target.value))}
          InputProps={{ inputProps: { min: 1, max: 20 } }}
          fullWidth
        />

        <FormControl fullWidth>
          <InputLabel id="difficulty-label">Difficulty</InputLabel>
          <Select
            labelId="difficulty-label"
            id="difficulty"
            value={difficulty}
            label="Difficulty"
            onChange={(e) => setDifficulty(e.target.value as string)}
          >
            <MenuItem value="easy">Easy</MenuItem>
            <MenuItem value="medium">Medium</MenuItem>
            <MenuItem value="hard">Hard</MenuItem>
          </Select>
        </FormControl>
      </div>

      <div className="mb-6">
        <LLMProviderSelector
          selectedProvider={llmProvider}
          selectedModel={selectedModel}
          onProviderChange={setLlmProvider}
          onModelChange={setSelectedModel}
          disabled={loading || isSubmitting}
        />
      </div>

      {generatedFlashcards && generatedFlashcards.length > 0 && (
        <div className="border rounded-md p-4 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Generated Flashcards ({generatedFlashcards.length})</h3>
            <button
              onClick={handleSave}
              disabled={loading || !!savedFlashcardSet}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
            >
              {savedFlashcardSet ? 'Saved' : (loading ? 'Saving...' : 'Save Flashcards')}
            </button>
          </div>


          
          <div className="space-y-6">
            {generatedFlashcards.map((flashcard, index) => (
              <div key={index} className="border p-3 rounded-md bg-gray-50">
                <div className="font-medium text-gray-700 mb-2">Flashcard #{index + 1}</div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Front Content:</label>
                    <textarea
                      value={flashcard.front_content || ''}
                      onChange={(e) => handleFlashcardChange(index, 'front_content', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Back Content:</label>
                    <textarea
                      value={flashcard.back_content || ''}
                      onChange={(e) => handleFlashcardChange(index, 'back_content', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FlashcardGeneratorForm;
